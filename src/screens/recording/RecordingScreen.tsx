import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
// import * as Sharing from 'expo-sharing';
import { colors, typography, spacing } from '../../styles';
import { Button } from '../../components/common';
import { useRecordingStore } from '../../stores';
import { useAudioRecorder, useAudioPlayer } from '../../hooks';
import { useLocation } from '../../hooks/useLocation';
import { testLocationServices } from '../../utils/locationTest';
import {
  formatDuration,
  // formatDurationWithMilliseconds,
  formatDate,
  formatFileSize,
} from '../../utils';
import { Recording } from '../../types/recording';
import { SwipeListView } from 'react-native-swipe-list-view';
import { TranscriptionModal } from '../../components/recording/TranscriptionModal';
// import AudioWaveform from '../../components/recording/AudioWaveform';
import { LinearGradient } from 'expo-linear-gradient';
import Toast from 'react-native-toast-message';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types/navigation';

const RecordingScreen: React.FC = () => {
  const { recordings, addRecording, updateRecording, deleteRecording } =
    useRecordingStore();
  const {
    isRecording,
    // recordingDuration,
    // audioLevel,
    startRecording,
    stopRecording,
    hasPermission,
    requestPermission,
  } = useAudioRecorder();
  const {
    playbackStatus,
    playbackSpeed,
    // loadAudio,
    playPause,
    changeSpeed,
    forward,
    rewind,
    unload,
  } = useAudioPlayer();

  const {
    requestLocationPermission,
    getCurrentLocation,
    formatLocationForDisplay,
  } = useLocation();

  const [selectedRecording, setSelectedRecording] = useState<Recording | null>(
    null
  );
  const [isPlayerModalVisible, setIsPlayerModalVisible] = useState(false);
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false);
  const [newName, setNewName] = useState('');
  const [isTranscriptionModalVisible, setIsTranscriptionModalVisible] =
    useState(false);
  const [
    selectedRecordingForTranscription,
    setSelectedRecordingForTranscription,
  ] = useState<Recording | null>(null);

  const [refreshing, setRefreshing] = useState(false);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [searchText, setSearchText] = useState('');

  // 模拟转录进度更新
  useEffect(() => {
    const interval = setInterval(() => {
      recordings.forEach((recording) => {
        if (
          recording.transcriptionStatus === 'processing' &&
          recording.transcriptionProgress !== undefined
        ) {
          const newProgress = Math.min(
            (recording.transcriptionProgress || 0) + Math.random() * 5,
            100
          );
          if (newProgress >= 100) {
            // 转录完成
            updateRecording(recording.id, {
              transcriptionStatus: 'completed',
              transcriptionProgress: 100,
            });
          } else {
            // 更新进度
            updateRecording(recording.id, {
              transcriptionProgress: newProgress,
            });
          }
        }
      });
    }, 2000); // 每2秒更新一次

    return () => clearInterval(interval);
  }, [recordings, updateRecording]);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    setTimeout(() => {
      // 你的数据获取逻辑，例如：
      // useRecordingStore.getState().fetchRecordings();
      setRefreshing(false);
      Toast.show({
        type: 'success',
        text1: '刷新成功',
      });
    }, 1000);
  }, []);

  const navigation = useNavigation<StackNavigationProp<RootStackParamList>>();

  useEffect(() => {
    // 检查录音权限
    if (!hasPermission) {
      requestPermission();
    }
  }, [hasPermission]);

  // 测试定位功能
  const handleTestLocation = async () => {
    console.log('开始测试定位功能...');
    const success = await testLocationServices();
    if (success) {
      Toast.show({
        type: 'success',
        text1: '定位测试成功',
        text2: '定位功能正常工作',
      });
    } else {
      Toast.show({
        type: 'error',
        text1: '定位测试失败',
        text2: '请检查定位权限和服务设置',
      });
    }
  };

  const handleStartRecording = async () => {
    if (isRecording) {
      await stopRecording();
    } else {
      // 在开始录音前请求定位权限（不阻塞录音）
      requestLocationPermission().catch((error) => {
        console.warn('Location permission request failed:', error);
        // 定位权限失败不影响录音功能
      });

      await startRecording();
    }
  };

  const handleImportAudio = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets.length > 0) {
        const asset = result.assets[0];

        // 获取当前位置信息
        let locationString: string | undefined;
        try {
          const locationData = await getCurrentLocation();
          if (locationData) {
            const formattedLocation = formatLocationForDisplay(locationData);
            locationString = formattedLocation || undefined;
          }
        } catch (locationError) {
          console.warn(
            'Failed to get location for imported audio:',
            locationError
          );
          // 位置获取失败不影响音频导入
        }

        const recording: Recording = {
          id: Date.now().toString(),
          title: asset.name || '导入的音频',
          filePath: asset.uri,
          duration: 0, // TODO: 获取实际时长
          size: asset.size || 0,
          createdAt: new Date(),
          updatedAt: new Date(),
          userId: 'current-user',
          type: 'import',
          transcriptionStatus: 'processing', // 导入的音频也默认为转文字状态
          transcriptionProgress: 0,
          location: locationString, // 使用真实位置信息，如果获取失败则为 undefined
        };

        addRecording(recording);
        Alert.alert('导入成功', '音频文件已导入');
      }
    } catch (error) {
      console.error('Import audio error:', error);
      Alert.alert('导入失败', '无法导入音频文件');
    }
  };

  // const handlePlayRecording = async (recording: Recording) => {
  //   setSelectedRecording(recording);
  //   setIsPlayerModalVisible(true);
  //   await loadAudio(recording.filePath);
  // };

  const handleRenameRecording = (recording: Recording) => {
    setSelectedRecording(recording);
    setNewName(recording.title);
    setIsRenameModalVisible(true);
  };

  const handleDeleteRecording = (recording: Recording) => {
    Alert.alert('删除录音', '确定要删除这个录音吗？', [
      { text: '取消', style: 'cancel' },
      {
        text: '删除',
        style: 'destructive',
        onPress: () => deleteRecording(recording.id),
      },
    ]);
  };

  // const handleShareRecording = async (recording: Recording) => {
  //   try {
  //     const isAvailable = await Sharing.isAvailableAsync();
  //     if (isAvailable) {
  //       await Sharing.shareAsync(recording.filePath);
  //     } else {
  //       Alert.alert('分享失败', '当前设备不支持分享功能');
  //     }
  //   } catch (error) {
  //     console.error('Share error:', error);
  //     Alert.alert('分享失败', '无法分享录音文件');
  //   }
  // };

  const handleSaveRename = () => {
    if (selectedRecording && newName.trim()) {
      updateRecording(selectedRecording.id, { title: newName.trim() });
      setIsRenameModalVisible(false);
      setSelectedRecording(null);
      setNewName('');
      Toast.show({
        type: 'success',
        text1: '修改成功',
      });
    }
  };

  const filteredRecordings = recordings.filter((recording) =>
    recording.title.toLowerCase().includes(searchText.toLowerCase())
  );

  // 渲染转录状态
  const renderTranscriptionStatus = (recording: Recording) => {
    switch (recording.transcriptionStatus) {
      case 'processing':
        return (
          <View style={styles.transcriptionStatusContainer}>
            <Image
              source={require('../../../assets/images/icons/vuesax／bold／arrow-right.png')}
              style={styles.transcriptionArrowIcon}
            />
            <Text style={styles.transcriptionStatusText}>转文字</Text>
          </View>
        );
      case 'completed':
        return (
          <View style={styles.statusButton}>
            <Ionicons
              name="checkmark-circle"
              size={16}
              color="#00C853"
              style={styles.statusButtonIcon}
            />
            <Text style={styles.statusButtonText}>已完成</Text>
          </View>
        );
      case 'failed':
        return (
          <View style={styles.statusButton}>
            <Ionicons
              name="close-circle"
              size={16}
              color="#F44336"
              style={styles.statusButtonIcon}
            />
            <Text style={[styles.statusButtonText, { color: '#F44336' }]}>
              转录失败
            </Text>
          </View>
        );
      case 'pending':
      default:
        return (
          <View style={styles.transcriptionStatusContainer}>
            <Image
              source={require('../../../assets/images/icons/vuesax／bold／arrow-right.png')}
              style={styles.transcriptionArrowIcon}
            />
            <Text style={styles.transcriptionStatusText}>转文字</Text>
          </View>
        );
    }
  };

  const renderRecordingItem = (data: { item: Recording }) => (
    <View key={data.item.id} style={styles.recordingItemContainer}>
      <View style={styles.recordingItemInfoContainer}>
        <Text style={styles.recordingItemTitle}>{data.item.title}</Text>
        <View style={styles.recordingItemDetails}>
          <Ionicons
            name="time-outline"
            size={15}
            color={colors.text.secondary}
            style={{ marginRight: 4 }}
          />
          <Text style={styles.recordingItemDesc}>
            {formatDuration(data.item.duration)}
          </Text>
          <Ionicons
            name="calendar-outline"
            size={15}
            color={colors.text.secondary}
            style={{ marginRight: 4 }}
          />
          <Text style={styles.recordingItemDesc}>
            {data.item.createdAt ? formatDate(data.item.createdAt) : '未知日期'}
            ·{data.item.type === 'import' ? '导入' : 'APP'}
          </Text>
          <Text style={styles.recordingItemDesc}>
            {formatFileSize(data.item.size)}
          </Text>
        </View>
        {/* 位置信息 */}
        {data.item.location && (
          <View style={styles.locationContainer}>
            <Ionicons
              name="location-outline"
              size={14}
              color={colors.text.secondary}
              style={{ marginRight: 4 }}
            />
            <Text style={styles.locationText}>{data.item.location}</Text>
          </View>
        )}
      </View>
      {/* 转录状态 */}
      <View style={styles.recordingItemActionsContainer}>
        {renderTranscriptionStatus(data.item)}
      </View>
    </View>
  );

  const renderHiddenItem = (data: { item: Recording }) => (
    <View style={styles.rowBack}>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnLeft]}
        onPress={() => handleRenameRecording(data.item)}
      >
        <View style={styles.editBtn}>
          <Ionicons name="create-outline" size={24} color="" />
        </View>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.backRightBtn, styles.backRightBtnRight]}
        onPress={() => handleDeleteRecording(data.item)}
      >
        <View style={styles.deleteBtn}>
          <Ionicons name="trash-outline" size={24} color="#fff" />
        </View>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.recordingSection}>
        <Text style={styles.recordingHeaderTitle}>录音</Text>
        {/* 按钮区 */}
        <View style={styles.actionButtons}>
          <Button
            style={styles.recordingActionButton}
            onPress={handleStartRecording}
          >
            <LinearGradient
              colors={[
                '#3053ED', // #3053ED 0%
                'rgba(82,121,255,0.8)', // rgba(82,121,255,0.8) 50%
                '#3EDDB6', // #3EDDB6 100%
              ]}
              locations={[0, 0.5, 1]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.gradientButton}
            >
              {isRecording ? (
                <Ionicons name="stop-sharp" size={24} color="#fff" />
              ) : (
                <Image
                  source={require('../../../assets/images/icons/microphone.png')}
                  style={styles.microphoneIcon}
                />
              )}
              <Text style={styles.recordingActionButtonText}>
                {isRecording ? '停止录音' : '开始录音'}
              </Text>
            </LinearGradient>
          </Button>
          <Button
            title="导入音频"
            style={styles.importAudioButton}
            onPress={handleImportAudio}
            icon={
              <Image
                source={require('../../../assets/images/icons/import.png')}
                style={styles.importAudioButtonIcon}
              />
            }
          />
          <Button
            title="测试定位"
            style={[styles.importAudioButton, { backgroundColor: '#FF9800' }]}
            onPress={handleTestLocation}
            icon={<Ionicons name="location" size={20} color="#fff" />}
          />
        </View>
      </View>

      <View style={styles.recordingContainer}>
        {/* Tab栏 */}
        <View style={styles.tabBarContainer}>
          {isSearchVisible ? (
            <TextInput
              style={styles.searchInput}
              placeholder="搜索录音..."
              value={searchText}
              onChangeText={setSearchText}
              autoFocus
              onBlur={() => setIsSearchVisible(false)}
            />
          ) : (
            <Text style={styles.tabBarText}>录音</Text>
          )}
          <View style={styles.tarBarIcon}>
            <TouchableOpacity
              onPress={() => setIsSearchVisible(!isSearchVisible)}
            >
              {isSearchVisible ? (
                <Ionicons
                  name="close-outline"
                  size={24}
                  color={colors.text.primary}
                />
              ) : (
                <Ionicons name="search" size={24} color={colors.text.primary} />
              )}
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => navigation.navigate('FileBatchManage')}
            >
              <Ionicons name="list" size={24} color={colors.text.primary} />
            </TouchableOpacity>
          </View>
        </View>
        {/* 录音列表 */}
        <View style={styles.listContainer}>
          {filteredRecordings.length === 0 ? (
            <View style={styles.emptyStateContainer}>
              <Ionicons
                name="mic-outline"
                size={48}
                color={colors.text.secondary}
              />
              <Text style={styles.emptyStateTitle}>暂无录音文件</Text>
              <Text style={styles.emptyStateSubtitle}>
                点击上方按钮开始录音或导入音频文件
              </Text>
            </View>
          ) : (
            <SwipeListView
              data={filteredRecordings}
              renderItem={renderRecordingItem}
              renderHiddenItem={renderHiddenItem}
              rightOpenValue={-120}
              previewRowKey={'0'}
              previewOpenValue={-40}
              previewOpenDelay={3000}
              style={styles.recordingListContainer}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              onRefresh={onRefresh}
              refreshing={refreshing}
            />
          )}
        </View>
      </View>

      {/* 音频播放器弹窗 */}
      <Modal
        visible={isPlayerModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => {
          unload();
          setIsPlayerModalVisible(false);
        }}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.playerModal}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>{selectedRecording?.title}</Text>
              <TouchableOpacity
                onPress={() => {
                  unload();
                  setIsPlayerModalVisible(false);
                }}
              >
                <Ionicons name="close" size={24} color={colors.text.primary} />
              </TouchableOpacity>
            </View>

            <View style={styles.playerContent}>
              {/* 播放进度 */}
              <View style={styles.progressContainer}>
                <Text style={styles.timeText}>
                  {formatDuration(
                    Math.floor(playbackStatus.positionMillis / 1000)
                  )}
                </Text>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width:
                          playbackStatus.durationMillis > 0
                            ? `${(playbackStatus.positionMillis / playbackStatus.durationMillis) * 100}%`
                            : '0%',
                      },
                    ]}
                  />
                </View>
                <Text style={styles.timeText}>
                  {formatDuration(
                    Math.floor(playbackStatus.durationMillis / 1000)
                  )}
                </Text>
              </View>

              {/* 播放控制 */}
              <View style={styles.playerControls}>
                <TouchableOpacity
                  onPress={() => rewind()}
                  style={styles.controlButton}
                >
                  <Ionicons
                    name="play-back"
                    size={24}
                    color={colors.text.primary}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={playPause}
                  style={[styles.controlButton, styles.playButton]}
                >
                  <Ionicons
                    name={playbackStatus.isPlaying ? 'pause' : 'play'}
                    size={32}
                    color={colors.background.primary}
                  />
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={() => forward()}
                  style={styles.controlButton}
                >
                  <Ionicons
                    name="play-forward"
                    size={24}
                    color={colors.text.primary}
                  />
                </TouchableOpacity>
              </View>

              {/* 播放速度 */}
              <View style={styles.speedControl}>
                <Text style={styles.speedLabel}>播放速度</Text>
                <View style={styles.speedButtons}>
                  {[0.5, 1.0, 1.5, 2.0].map((speed) => (
                    <TouchableOpacity
                      key={speed}
                      style={[
                        styles.speedButton,
                        playbackSpeed === speed && styles.speedButtonActive,
                      ]}
                      onPress={() => changeSpeed(speed)}
                    >
                      <Text
                        style={[
                          styles.speedButtonText,
                          playbackSpeed === speed &&
                            styles.speedButtonTextActive,
                        ]}
                      >
                        {speed}x
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* 重命名弹窗 */}
      <Modal
        visible={isRenameModalVisible}
        animationType="fade"
        transparent={true}
        onRequestClose={() => setIsRenameModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.renameModal}>
            <Text style={styles.modalTitle}>重命名</Text>
            <TextInput
              style={styles.renameInput}
              value={newName}
              onChangeText={setNewName}
              placeholder="请输入新的录音名称"
              autoFocus
            />
            <View style={styles.modalButtons}>
              <Button
                style={styles.modalButton}
                onPress={() => setIsRenameModalVisible(false)}
                title="取消"
              />
              <Button
                variant="primary"
                style={styles.modalButton}
                onPress={handleSaveRename}
              >
                <Text
                  style={[
                    styles.modalButtonText,
                    styles.modalButtonTextConfirm,
                  ]}
                >
                  确定
                </Text>
              </Button>
            </View>
          </View>
        </View>
      </Modal>

      {/* 转写和AI分析弹窗 */}
      <TranscriptionModal
        visible={isTranscriptionModalVisible}
        recording={selectedRecordingForTranscription}
        onClose={() => {
          setIsTranscriptionModalVisible(false);
          setSelectedRecordingForTranscription(null);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: spacing.xl,
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  recordingSection: {
    padding: spacing.lg,
    backgroundColor: colors.background.bg,
  },

  recordingHeaderTitle: {
    marginBottom: spacing.md,
    fontSize: typography.fontSize['2xl'],
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
  },

  recordingActionButton: {
    width: 203,
    height: 64,
    borderRadius: 20,
    overflow: 'hidden',
    paddingVertical: 0,
    paddingHorizontal: 0,
    // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4, // Android 阴影
  },

  gradientButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    borderRadius: 20,
    // CSS: background: linear-gradient(...), #FFFFFF;
    // 白色作为后备背景色
    backgroundColor: '#FFFFFF',
  },

  recordingActionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: typography.fontWeight.semibold,
    marginLeft: 8,
  },

  microphoneIcon: {
    width: 24,
    height: 24,
    tintColor: '#fff', // 设置图标颜色为白色
  },

  importAudioButton: {
    width: 132, // 固定宽度 132px
    height: 64, // 固定高度 64px
    backgroundColor: '#FFFFFF', // background: #FFFFFF
    borderRadius: 20, // border-radius: 20px
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 0,
    paddingHorizontal: 0,
    // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
    shadowColor: 'rgba(100, 103, 122, 0.06)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 4, // Android 阴影
    // 移除边框，使用阴影效果
  },

  importAudioButtonIcon: {
    width: 20,
    height: 20,
    marginRight: 6, // 减少间距以适应较小的按钮宽度
    tintColor: '#00C853', // 设置图标颜色为绿色
  },

  importAudioButtonText: {
    color: '#333',
    fontSize: 14, // 减小字体以适应较小的按钮
    fontWeight: typography.fontWeight.semibold,
  },

  recordingContainer: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 12,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    overflow: 'hidden',
  },

  tabBarContainer: {
    flexDirection: 'row',
    marginLeft: 24,
    marginBottom: 8,
    justifyContent: 'space-between',
    marginRight: 24,
  },

  tabBarText: {
    fontSize: 18,
    fontWeight: typography.fontWeight.semibold,
    borderBottomWidth: 3,
    borderBottomColor: colors.primary,
    paddingBottom: 12,
  },

  searchInput: {
    flex: 1,
    fontSize: 16,
    borderBottomWidth: 1,
    borderColor: colors.border,
    paddingVertical: 8,
  },

  tarBarIcon: {
    gap: 8,
    flexDirection: 'row',
  },

  listContainer: {
    paddingHorizontal: 0,
    flex: 1,
  },

  emptyStateContainer: {
    alignItems: 'center',
    paddingVertical: 64,
  },

  emptyStateTitle: {
    fontSize: 18,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 4,
  },

  emptyStateSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
  },

  recordingListContainer: {
    paddingHorizontal: 16,
  },

  recordingItemContainer: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },

  rowBack: {
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  backRightBtn: {
    alignItems: 'center',
    bottom: 0,
    justifyContent: 'center',
    position: 'absolute',
    top: 0,
    width: 60,
  },
  backRightBtnLeft: {
    // backgroundColor: colors.primary,
    right: 55,
  },
  backRightBtnRight: {
    right: 0,
  },

  editBtn: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    backgroundColor: '#fff',
  },

  deleteBtn: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 50,
    backgroundColor: 'red',
  },

  recordingItemIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  recordingItemInfoContainer: {
    flex: 1,
  },

  recordingItemTitle: {
    fontSize: 16,
    fontWeight: typography.fontWeight.semibold,
    color: colors.text.primary,
    marginBottom: 2,
  },

  recordingItemDetails: {
    alignItems: 'center',
    flexDirection: 'row',
    color: colors.text.secondary,
  },

  recordingItemDesc: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 8,
  },

  recordingItemActionsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.secondary,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },

  statusButtonIcon: {
    marginRight: 4,
  },

  statusButtonText: {
    color: '#00C853',
    fontSize: 14,
  },

  // 转录处理中状态样式
  processingIndicator: {
    backgroundColor: '#E3F2FD',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },

  processingText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: typography.fontWeight.medium,
  },

  // 位置信息样式
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },

  locationText: {
    fontSize: 12,
    color: colors.text.secondary,
    flex: 1,
  },

  // 转录状态容器样式（灰色圆角背景，和已完成状态一样）
  transcriptionStatusContainer: {
    width: 83,
    height: 32,
    backgroundColor: '#F2F3F5',
    borderRadius: 999,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },

  // 转录箭头图标样式（PNG切图，不需要设置颜色）
  transcriptionArrowIcon: {
    width: 20,
    height: 20,
    marginRight: 4,
    // 不设置 tintColor，保持PNG原始颜色（蓝色背景+白色箭头）
  },

  // 转录状态文字样式（按照Android TextView样式）
  transcriptionStatusText: {
    width: 47, // android:layout_width="47dp"
    height: 22, // android:layout_height="22dp"
    color: '#2A2B33', // android:textColor="#ff2a2b33"
    fontSize: 12, // android:textSize="12sp"
    textAlign: 'center',
  },

  editButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#F5F5F5',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },

  deleteButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#FF5252',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 4,
  },

  actionButtonIcon: {
    // This can be an empty object if no specific style is needed,
    // or you can add common styles for all action icons.
  },

  // 功能特色区域
  featuresSection: {
    paddingHorizontal: 20,
    paddingVertical: 24,
    backgroundColor: '#FAFAFA',
    marginBottom: 100, // 给底部导航留出空间
  },

  featuresList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },

  featureItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },

  featureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },

  featureIconText: {
    fontSize: 16,
    fontWeight: '600',
  },

  featureText: {
    fontSize: 14,
    color: colors.text.primary,
    fontWeight: '500',
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
  },

  playerModal: {
    backgroundColor: colors.background.primary,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingBottom: 40,
    maxHeight: '80%',
  },

  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 20,
  },

  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginRight: spacing.md,
  },

  playerContent: {
    paddingHorizontal: 20,
  },

  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },

  timeText: {
    fontSize: 12,
    color: colors.text.secondary,
    minWidth: 45,
  },

  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
    marginHorizontal: 12,
    overflow: 'hidden',
  },

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },

  playerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },

  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 12,
  },

  playButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.primary,
    marginHorizontal: 20,
  },

  speedControl: {
    marginTop: 20,
  },

  speedLabel: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 12,
    textAlign: 'center',
  },

  speedButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },

  speedButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 4,
    borderRadius: 16,
    backgroundColor: '#F5F5F5',
  },

  speedButtonActive: {
    backgroundColor: colors.primary,
  },

  speedButtonText: {
    fontSize: 14,
    color: colors.text.primary,
  },

  speedButtonTextActive: {
    color: colors.background.primary,
    fontWeight: '500',
  },

  // Rename modal styles
  renameModal: {
    backgroundColor: colors.background.primary,
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 20,
  },

  renameInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: colors.text.primary,
    marginVertical: 16,
  },

  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: spacing.md,
    gap: spacing.md,
  },

  modalButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: spacing.lg,
    flex: 1,
  },

  modalButtonCancel: {
    backgroundColor: '#F5F5F5',
  },

  modalButtonConfirm: {
    backgroundColor: colors.primary,
  },

  modalButtonText: {
    fontSize: 16,
    color: colors.text.primary,
  },

  modalButtonTextConfirm: {
    color: colors.background.primary,
    fontWeight: '500',
  },
});

export default RecordingScreen;
