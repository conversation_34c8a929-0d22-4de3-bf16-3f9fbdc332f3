import React, { createContext, useContext, useState, ReactNode } from 'react';

interface RecordingContextType {
  // 胶囊状态
  isRecordingCapsuleVisible: boolean;
  setIsRecordingCapsuleVisible: (visible: boolean) => void;
  
  // 录音状态
  isRecording: boolean;
  setIsRecording: (recording: boolean) => void;
  
  isPaused: boolean;
  setIsPaused: (paused: boolean) => void;
  
  recordingDuration: number;
  setRecordingDuration: (duration: number) => void;
  
  // 胶囊操作回调
  onCapsulePress?: () => void;
  setOnCapsulePress: (callback: (() => void) | undefined) => void;
  
  onCapsuleStop?: () => void;
  setOnCapsuleStop: (callback: (() => void) | undefined) => void;
}

const RecordingContext = createContext<RecordingContextType | undefined>(undefined);

interface RecordingProviderProps {
  children: ReactNode;
}

export const RecordingProvider: React.FC<RecordingProviderProps> = ({ children }) => {
  const [isRecordingCapsuleVisible, setIsRecordingCapsuleVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [onCapsulePress, setOnCapsulePress] = useState<(() => void) | undefined>();
  const [onCapsuleStop, setOnCapsuleStop] = useState<(() => void) | undefined>();

  const value: RecordingContextType = {
    isRecordingCapsuleVisible,
    setIsRecordingCapsuleVisible,
    isRecording,
    setIsRecording,
    isPaused,
    setIsPaused,
    recordingDuration,
    setRecordingDuration,
    onCapsulePress,
    setOnCapsulePress,
    onCapsuleStop,
    setOnCapsuleStop,
  };

  return (
    <RecordingContext.Provider value={value}>
      {children}
    </RecordingContext.Provider>
  );
};

export const useRecordingContext = () => {
  const context = useContext(RecordingContext);
  if (context === undefined) {
    throw new Error('useRecordingContext must be used within a RecordingProvider');
  }
  return context;
};
