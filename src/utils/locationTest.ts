import * as Location from 'expo-location';

// 简单的定位测试函数，用于调试
export const testLocationServices = async () => {
  console.log('=== 开始定位服务测试 ===');

  try {
    // 1. 检查定位服务是否可用
    const servicesEnabled = await Location.hasServicesEnabledAsync();
    console.log('定位服务是否开启:', servicesEnabled);

    if (!servicesEnabled) {
      console.log('❌ 定位服务未开启，请在设备设置中开启定位服务');
      return false;
    }

    // 2. 检查当前权限状态
    const { status: currentStatus } =
      await Location.getForegroundPermissionsAsync();
    console.log('当前权限状态:', currentStatus);

    // 3. 如果没有权限，请求权限
    let finalStatus = currentStatus;
    if (currentStatus !== 'granted') {
      console.log('请求定位权限...');
      const { status } = await Location.requestForegroundPermissionsAsync();
      finalStatus = status;
      console.log('权限请求结果:', status);
    }

    if (finalStatus !== 'granted') {
      console.log('❌ 定位权限被拒绝');
      return false;
    }

    // 4. 尝试获取当前位置
    console.log('尝试获取当前位置...');
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Low,
        mayShowUserSettingsDialog: true,
      });

      console.log('✅ 成功获取当前位置:');
      console.log('  纬度:', location.coords.latitude);
      console.log('  经度:', location.coords.longitude);
      console.log('  精度:', location.coords.accuracy);
      console.log('  时间戳:', new Date(location.timestamp).toLocaleString());

      // 5. 尝试反向地理编码
      try {
        console.log('尝试获取地址信息...');
        const addresses = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (addresses.length > 0) {
          const address = addresses[0];
          console.log('✅ 成功获取地址信息:');
          console.log('  国家:', address.country);
          console.log('  城市:', address.city);
          console.log('  区域:', address.district);
          console.log('  街道:', address.street);
          console.log('  详细地址:', address.name);

          const addressParts = [
            address.city,
            address.district,
            address.street,
            address.name,
          ].filter(Boolean);

          const fullAddress = addressParts.join('');
          console.log('  完整地址:', fullAddress);
        } else {
          console.log('⚠️ 未获取到地址信息');
        }
      } catch (geocodeError) {
        console.log('⚠️ 地理编码失败:', geocodeError);
      }

      return true;
    } catch (locationError) {
      console.log('❌ 获取当前位置失败:', locationError);

      // 6. 尝试获取最后已知位置
      console.log('尝试获取最后已知位置...');
      try {
        const lastLocation = await Location.getLastKnownPositionAsync();
        if (lastLocation) {
          console.log('✅ 成功获取最后已知位置:');
          console.log('  纬度:', lastLocation.coords.latitude);
          console.log('  经度:', lastLocation.coords.longitude);
          console.log(
            '  时间戳:',
            new Date(lastLocation.timestamp).toLocaleString()
          );
          return true;
        } else {
          console.log('❌ 无最后已知位置');
          return false;
        }
      } catch (lastLocationError) {
        console.log('❌ 获取最后已知位置失败:', lastLocationError);
        return false;
      }
    }
  } catch (error) {
    console.log('❌ 定位测试失败:', error);
    return false;
  } finally {
    console.log('=== 定位服务测试结束 ===');
  }
};
