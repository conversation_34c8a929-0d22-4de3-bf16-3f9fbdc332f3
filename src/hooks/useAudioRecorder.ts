import { useState, useEffect, useRef } from 'react';
import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { useRecordingStore } from '../stores';
import { Recording } from '../types/recording';
import { generateId } from '../utils';
import Toast from 'react-native-toast-message';
import { useAudioMode } from './useAudioMode';
import { useLocation } from './useLocation';

export const useAudioRecorder = () => {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0.5);
  const durationInterval = useRef<NodeJS.Timeout>();

  const { addRecording } = useRecordingStore();
  const { enableRecordingMode, disableRecordingMode } = useAudioMode();
  const { getCachedLocation, formatLocationForDisplay } = useLocation();

  // 检查和请求录音权限
  const requestPermission = async () => {
    try {
      const { granted } = await Audio.requestPermissionsAsync();
      setHasPermission(granted);

      if (!granted) {
        Toast.show({
          type: 'error',
          text1: '权限被拒绝',
          text2: '请在设置中允许应用访问麦克风',
        });
      }

      return { granted };
    } catch (error) {
      console.error('Failed to request permission', error);
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求麦克风权限',
      });
      return { granted: false };
    }
  };

  useEffect(() => {
    // 初始化时检查权限
    requestPermission();
  }, []);

  useEffect(() => {
    // 更新录音时长（精确到毫秒）
    if (isRecording) {
      durationInterval.current = setInterval(() => {
        setRecordingDuration((prev) => prev + 0.1); // 100ms间隔
      }, 100);
    } else {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    }

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, [isRecording]);

  // 模拟音频电平变化（在真实实现中应该从录音状态获取）
  useEffect(() => {
    let levelInterval: NodeJS.Timeout;
    if (isRecording) {
      levelInterval = setInterval(() => {
        setAudioLevel(Math.random() * 0.8 + 0.2); // 0.2 到 1.0 之间
      }, 100);
    } else {
      setAudioLevel(0.5);
    }

    return () => {
      if (levelInterval) clearInterval(levelInterval);
    };
  }, [isRecording]);

  // 组件卸载时清理定时器（录音实例的清理由 stopRecording 处理）
  useEffect(() => {
    return () => {
      // 清理定时器
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      // 检查权限
      if (!hasPermission) {
        const permission = await requestPermission();
        if (!permission.granted) {
          return;
        }
      }

      // 如果已经在录音，先停止并等待完成
      if (recording) {
        await stopRecording();
        // 等待一小段时间确保资源完全释放
        await new Promise((resolve) => setTimeout(resolve, 100));
      }

      // 启用录音模式
      await enableRecordingMode();

      // 创建录音配置
      const recordingOptions = Audio.RecordingOptionsPresets.HIGH_QUALITY;

      // 创建录音实例
      const newRecording = new Audio.Recording();

      try {
        await newRecording.prepareToRecordAsync(recordingOptions);
        await newRecording.startAsync();

        setRecording(newRecording);
        setIsRecording(true);
        setRecordingDuration(0);
      } catch (prepareError) {
        // 如果准备或开始录音失败，清理录音实例
        try {
          await newRecording.stopAndUnloadAsync();
        } catch (cleanupError) {
          console.error('Failed to cleanup failed recording:', cleanupError);
        }
        throw prepareError;
      }

      // 移除录音开始提示
    } catch (error) {
      console.error('Failed to start recording', error);
      // 移除录音失败提示
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    let newRecording: Recording | undefined;

    try {
      setIsRecording(false);

      // 停止录音
      await recording.stopAndUnloadAsync();

      // 获取录音文件信息
      const uri = recording.getURI();
      if (!uri) {
        throw new Error('Recording URI is null');
      }

      // 获取文件大小
      const info = await FileSystem.getInfoAsync(uri);
      const fileSize = info.exists && 'size' in info ? info.size : 0;

      // 获取缓存的位置信息（不阻塞录音保存）
      let locationString: string | undefined;
      try {
        const locationData = getCachedLocation();
        if (locationData) {
          const formattedLocation = formatLocationForDisplay(locationData);
          locationString = formattedLocation || undefined;
        }
      } catch (locationError) {
        console.warn(
          'Failed to get cached location for recording:',
          locationError
        );
        // 位置获取失败不影响录音保存
      }

      // 创建录音记录
      newRecording = {
        id: generateId(),
        title: `录音_${new Date().toLocaleString('zh-CN')}`,
        filePath: uri,
        duration: recordingDuration,
        size: fileSize,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: 'current-user',
        type: 'recording',
        transcriptionStatus: 'processing', // 默认为转文字状态
        transcriptionProgress: 0,
        location: locationString, // 使用真实位置信息，如果获取失败则为 undefined
      };

      // 保存到状态管理
      addRecording(newRecording);

      // 移除录音完成提示
    } catch (error) {
      console.error('Failed to stop recording', error);
      // 移除录音保存失败提示
    } finally {
      // 确保在任何情况下都清理资源和重置状态
      setRecording(null);
      setRecordingDuration(0);

      // 清理定时器
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }

      // 禁用录音模式，释放麦克风资源
      await disableRecordingMode();
    }

    return newRecording;
  };

  const pauseRecording = async () => {
    if (recording && isRecording) {
      try {
        await recording.pauseAsync();
        setIsRecording(false);
        // 移除暂停提示
      } catch (error) {
        console.error('Failed to pause recording', error);
        // 移除暂停失败提示
      }
    }
  };

  const resumeRecording = async () => {
    if (recording && !isRecording) {
      try {
        await recording.startAsync();
        setIsRecording(true);
        // 移除继续录音提示
      } catch (error) {
        console.error('Failed to resume recording', error);
        // 移除继续失败提示
        // 如果恢复失败，可能需要重置状态
        setRecording(null);
        setIsRecording(false);
        setRecordingDuration(0);
      }
    }
  };

  return {
    isRecording,
    recordingDuration,
    audioLevel,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    hasPermission,
    requestPermission,
  };
};
