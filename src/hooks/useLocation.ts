import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import Toast from 'react-native-toast-message';

export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: number;
}

export interface LocationState {
  currentLocation: LocationData | null;
  hasLocationPermission: boolean;
  isLoadingLocation: boolean;
  locationError: string | null;
}

export const useLocation = () => {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);

  // 请求定位权限
  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasLocationPermission(granted);

      if (!granted) {
        setLocationError('定位权限被拒绝');
        Toast.show({
          type: 'error',
          text1: '定位权限被拒绝',
          text2: '请在设置中允许应用访问位置信息',
        });
      } else {
        setLocationError(null);
      }

      return granted;
    } catch (error) {
      console.error('Failed to request location permission:', error);
      setLocationError('请求定位权限失败');
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求定位权限',
      });
      return false;
    }
  };

  // 获取当前位置
  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLoadingLocation(true);
      setLocationError(null);

      // 检查权限
      if (!hasLocationPermission) {
        const granted = await requestLocationPermission();
        if (!granted) {
          return null;
        }
      }

      // 获取位置
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 5000,
        distanceInterval: 10,
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: location.timestamp,
      };

      // 尝试获取地址信息
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (addresses.length > 0) {
          const address = addresses[0];
          // 构建地址字符串
          const addressParts = [
            address.city,
            address.district,
            address.street,
            address.name,
          ].filter(Boolean);

          locationData.address = addressParts.join('');
        }
      } catch (geocodeError) {
        console.warn('Failed to get address:', geocodeError);
        // 地址获取失败不影响位置数据
      }

      setCurrentLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Failed to get current location:', error);
      setLocationError('获取位置信息失败');
      Toast.show({
        type: 'error',
        text1: '定位失败',
        text2: '无法获取当前位置信息',
      });
      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // 检查定位服务是否可用
  const checkLocationServicesEnabled = async (): Promise<boolean> => {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Toast.show({
          type: 'error',
          text1: '定位服务未开启',
          text2: '请在设备设置中开启定位服务',
        });
      }
      return enabled;
    } catch (error) {
      console.error('Failed to check location services:', error);
      return false;
    }
  };

  // 格式化地址显示
  const formatLocationForDisplay = (
    location: LocationData | null
  ): string | null => {
    if (!location) return null;

    if (location.address) {
      // 如果地址太长，截取前20个字符
      return location.address.length > 20
        ? `${location.address.substring(0, 20)}...`
        : location.address;
    }

    // 如果没有地址，显示经纬度
    return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
  };

  // 初始化时检查权限状态
  useEffect(() => {
    const checkInitialPermission = async () => {
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        setHasLocationPermission(status === 'granted');
      } catch (error) {
        console.error('Failed to check initial location permission:', error);
      }
    };

    checkInitialPermission();
  }, []);

  return {
    // 状态
    currentLocation,
    hasLocationPermission,
    isLoadingLocation,
    locationError,

    // 方法
    requestLocationPermission,
    getCurrentLocation,
    checkLocationServicesEnabled,
    formatLocationForDisplay,
  };
};
