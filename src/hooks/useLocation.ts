import { useState, useEffect } from 'react';
import * as Location from 'expo-location';
import Toast from 'react-native-toast-message';

export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  timestamp: number;
}

export interface LocationState {
  currentLocation: LocationData | null;
  hasLocationPermission: boolean;
  isLoadingLocation: boolean;
  locationError: string | null;
}

export const useLocation = () => {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(
    null
  );
  const [hasLocationPermission, setHasLocationPermission] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  // 请求定位权限
  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      // 先检查当前权限状态
      const { status: currentStatus } =
        await Location.getForegroundPermissionsAsync();

      if (currentStatus === 'granted') {
        setHasLocationPermission(true);
        setLocationError(null);
        return true;
      }

      // 请求权限
      const { status } = await Location.requestForegroundPermissionsAsync();
      const granted = status === 'granted';
      setHasLocationPermission(granted);

      if (!granted) {
        setLocationError('定位权限被拒绝');
        console.warn('Location permission denied, status:', status);
        Toast.show({
          type: 'error',
          text1: '定位权限被拒绝',
          text2: '请在设置中允许应用访问位置信息',
        });
      } else {
        setLocationError(null);
        console.log('Location permission granted');
      }

      return granted;
    } catch (error) {
      console.error('Failed to request location permission:', error);
      setLocationError('请求定位权限失败');
      Toast.show({
        type: 'error',
        text1: '权限请求失败',
        text2: '无法请求定位权限',
      });
      return false;
    }
  };

  // 获取当前位置
  const getCurrentLocation = async (): Promise<LocationData | null> => {
    try {
      setIsLoadingLocation(true);
      setLocationError(null);

      // 检查定位服务是否开启
      const servicesEnabled = await checkLocationServicesEnabled();
      if (!servicesEnabled) {
        return null;
      }

      // 检查权限
      if (!hasLocationPermission) {
        const granted = await requestLocationPermission();
        if (!granted) {
          return null;
        }
      }

      // 获取位置 - 使用更宽松的配置
      let location: Location.LocationObject;
      try {
        location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low, // 使用低精度，更容易获取
          mayShowUserSettingsDialog: true, // 允许显示设置对话框
        });
      } catch (currentLocationError) {
        console.warn(
          'getCurrentPositionAsync failed, trying getLastKnownPositionAsync:',
          currentLocationError
        );
        // 如果获取当前位置失败，尝试获取最后已知位置
        const lastKnownLocation = await Location.getLastKnownPositionAsync();

        if (!lastKnownLocation) {
          throw new Error('无法获取当前位置或最后已知位置');
        }

        location = lastKnownLocation;
      }

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        timestamp: location.timestamp,
      };

      // 尝试获取地址信息
      try {
        const addresses = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        if (addresses.length > 0) {
          const address = addresses[0];
          // 构建地址字符串
          const addressParts = [
            address.city,
            address.district,
            address.street,
            address.name,
          ].filter(Boolean);

          locationData.address = addressParts.join('');
        }
      } catch (geocodeError) {
        console.warn('Failed to get address:', geocodeError);
        // 地址获取失败不影响位置数据
      }

      setCurrentLocation(locationData);
      return locationData;
    } catch (error) {
      console.error('Failed to get current location:', error);

      let errorMessage = '无法获取当前位置信息';
      if (error instanceof Error) {
        if (error.message.includes('location services')) {
          errorMessage = '请在设备设置中开启定位服务';
        } else if (error.message.includes('permission')) {
          errorMessage = '定位权限被拒绝，请在设置中允许访问位置';
        } else if (error.message.includes('timeout')) {
          errorMessage = '定位超时，请稍后重试';
        }
      }

      setLocationError(errorMessage);
      Toast.show({
        type: 'error',
        text1: '定位失败',
        text2: errorMessage,
      });
      return null;
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // 检查定位服务是否可用
  const checkLocationServicesEnabled = async (): Promise<boolean> => {
    try {
      const enabled = await Location.hasServicesEnabledAsync();
      if (!enabled) {
        Toast.show({
          type: 'error',
          text1: '定位服务未开启',
          text2: '请在设备设置中开启定位服务',
        });
      }
      return enabled;
    } catch (error) {
      console.error('Failed to check location services:', error);
      return false;
    }
  };

  // 格式化地址显示
  const formatLocationForDisplay = (
    location: LocationData | null
  ): string | null => {
    if (!location) return null;

    if (location.address) {
      // 如果地址太长，截取前20个字符
      return location.address.length > 20
        ? `${location.address.substring(0, 20)}...`
        : location.address;
    }

    // 如果没有地址，显示经纬度
    return `${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
  };

  // 初始化位置信息（应用启动时调用一次）
  const initializeLocation = async () => {
    if (isInitialized) return; // 避免重复初始化

    try {
      setIsInitialized(true); // 立即设置为已初始化，避免重复调用
      setIsLoadingLocation(true);
      console.log('开始初始化位置信息...');

      // 检查权限
      const granted = await requestLocationPermission();
      if (!granted) {
        console.log('位置权限被拒绝，使用模拟位置');
        const mockLocation: LocationData = {
          latitude: 22.3193, // 深圳坐标
          longitude: 114.1694,
          address: '广东省深圳市南山区',
          timestamp: Date.now(),
        };
        setCurrentLocation(mockLocation);
        return;
      }

      // 尝试获取真实位置（只尝试一次）
      try {
        const location = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.Low,
          mayShowUserSettingsDialog: false, // 不显示设置对话框，避免用户交互
        });

        const locationData: LocationData = {
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          timestamp: location.timestamp,
        };

        // 尝试获取地址信息
        try {
          const reverseGeocode = await Location.reverseGeocodeAsync({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          });

          if (reverseGeocode.length > 0) {
            const address = reverseGeocode[0];
            locationData.address = [
              address.country,
              address.region,
              address.city,
              address.district,
            ]
              .filter(Boolean)
              .join('');
          }
        } catch (geocodeError) {
          console.warn('地址解析失败:', geocodeError);
        }

        setCurrentLocation(locationData);
        console.log('真实位置初始化成功:', locationData);
      } catch (locationError) {
        // 获取真实位置失败，使用模拟位置
        console.log('真实位置获取失败，使用模拟位置数据');
        const mockLocation: LocationData = {
          latitude: 22.3193, // 深圳坐标
          longitude: 114.1694,
          address: '广东省深圳市南山区',
          timestamp: Date.now(),
        };
        setCurrentLocation(mockLocation);
        console.log('模拟位置设置成功:', mockLocation);
      }
    } catch (error) {
      console.warn('位置初始化失败:', error);
      // 即使失败也设置模拟位置
      const mockLocation: LocationData = {
        latitude: 22.3193,
        longitude: 114.1694,
        address: '广东省深圳市南山区',
        timestamp: Date.now(),
      };
      setCurrentLocation(mockLocation);
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // 获取缓存的位置信息（用于录音保存）
  const getCachedLocation = (): LocationData | null => {
    return currentLocation;
  };

  // 初始化时检查权限状态
  useEffect(() => {
    const checkInitialPermission = async () => {
      try {
        const { status } = await Location.getForegroundPermissionsAsync();
        setHasLocationPermission(status === 'granted');
      } catch (error) {
        console.error('Failed to check initial location permission:', error);
      }
    };

    checkInitialPermission();
  }, []);

  return {
    // 状态
    currentLocation,
    hasLocationPermission,
    isLoadingLocation,
    locationError,
    isInitialized,

    // 方法
    requestLocationPermission,
    getCurrentLocation,
    getCachedLocation,
    initializeLocation,
    checkLocationServicesEnabled,
    formatLocationForDisplay,
  };
};
