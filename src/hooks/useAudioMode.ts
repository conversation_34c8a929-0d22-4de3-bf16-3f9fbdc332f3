import { useEffect, useRef } from 'react';
import { Audio } from 'expo-av';

/**
 * 管理音频模式的 Hook
 * 确保麦克风资源在不需要时被正确释放
 */
export const useAudioMode = () => {
  const isRecordingModeEnabled = useRef(false);

  // 启用录音模式
  const enableRecordingMode = async () => {
    if (isRecordingModeEnabled.current) return;

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
      isRecordingModeEnabled.current = true;
      console.log('Recording mode enabled');
    } catch (error) {
      console.error('Failed to enable recording mode:', error);
      throw error;
    }
  };

  // 禁用录音模式
  const disableRecordingMode = async () => {
    if (!isRecordingModeEnabled.current) return;

    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
      });
      isRecordingModeEnabled.current = false;
      console.log('Recording mode disabled');
    } catch (error) {
      console.error('Failed to disable recording mode:', error);
      // 不抛出错误，因为这是清理操作
    }
  };

  // 组件卸载时确保禁用录音模式
  useEffect(() => {
    return () => {
      if (isRecordingModeEnabled.current) {
        Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: false,
          playThroughEarpieceAndroid: false,
        }).catch((error) => {
          console.error('Failed to cleanup audio mode on unmount:', error);
        });
      }
    };
  }, []);

  return {
    enableRecordingMode,
    disableRecordingMode,
    isRecordingModeEnabled: () => isRecordingModeEnabled.current,
  };
};
