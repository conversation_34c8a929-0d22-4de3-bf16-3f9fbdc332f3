import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { PanGestureHandler } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { useRecordingContext } from '../../contexts/RecordingContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const GlobalRecordingCapsule: React.FC = () => {
  const {
    isRecordingCapsuleVisible,
    isRecording,
    isPaused,
    recordingDuration,
    onCapsulePress,
    onCapsuleStop,
  } = useRecordingContext();

  const translateX = useRef(new Animated.Value(screenWidth - 200)).current; // 默认右侧位置
  const translateY = useRef(new Animated.Value(100)).current; // 默认顶部位置

  // 格式化录音时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 处理拖拽手势
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX, translationY: translateY } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === 5) { // GESTURE_STATE_END
      const { translationX: finalX, translationY: finalY } = event.nativeEvent;
      
      // 计算最终位置，确保胶囊不会超出屏幕边界
      const capsuleWidth = 180;
      const capsuleHeight = 50;
      const safeMargin = 20;
      
      const maxX = screenWidth - capsuleWidth - safeMargin;
      const maxY = screenHeight - capsuleHeight - safeMargin - 100; // 减去底部导航栏高度
      
      const finalTranslateX = Math.max(safeMargin, Math.min(maxX, finalX));
      const finalTranslateY = Math.max(safeMargin + 50, Math.min(maxY, finalY)); // 顶部状态栏下方

      // 动画到最终位置
      Animated.parallel([
        Animated.spring(translateX, {
          toValue: finalTranslateX,
          useNativeDriver: true,
        }),
        Animated.spring(translateY, {
          toValue: finalTranslateY,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  // 当胶囊显示时，设置初始位置
  useEffect(() => {
    if (isRecordingCapsuleVisible) {
      // 设置到右上角位置
      Animated.parallel([
        Animated.timing(translateX, {
          toValue: screenWidth - 200,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(translateY, {
          toValue: 100,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isRecordingCapsuleVisible]);

  if (!isRecordingCapsuleVisible) return null;

  return (
    <View style={styles.overlay}>
      <PanGestureHandler
        onGestureEvent={onGestureEvent}
        onHandlerStateChange={onHandlerStateChange}
      >
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                { translateX },
                { translateY },
              ],
            },
          ]}
        >
          <TouchableOpacity 
            style={styles.capsule} 
            onPress={onCapsulePress} 
            activeOpacity={0.8}
          >
            {/* 左侧录音状态指示器 */}
            <View style={styles.leftSection}>
              <View style={styles.statusIndicator}>
                <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 1 : 0.3 }]} />
                <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.7 : 0.2 }]} />
                <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.4 : 0.1 }]} />
              </View>
            </View>

            {/* 中间时长和状态 */}
            <View style={styles.centerSection}>
              <Text style={styles.durationText}>{formatDuration(recordingDuration)}</Text>
              <Text style={styles.statusText}>
                {isPaused ? '已暂停' : isRecording ? '录音中' : '准备录音'}
              </Text>
            </View>

            {/* 右侧停止按钮 */}
            <TouchableOpacity 
              style={styles.rightSection} 
              onPress={onCapsuleStop}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <View style={styles.stopButton}>
                <Ionicons name="stop" size={16} color="#fff" />
              </View>
            </TouchableOpacity>
          </TouchableOpacity>
        </Animated.View>
      </PanGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999, // 最高层级
    pointerEvents: 'box-none', // 只有胶囊可以接收触摸事件
  },

  container: {
    position: 'absolute',
    zIndex: 10000,
  },

  capsule: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007bff',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    minWidth: 180,
  },

  leftSection: {
    marginRight: 12,
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  recordingDot: {
    width: 3,
    height: 12,
    backgroundColor: '#fff',
    marginHorizontal: 1,
    borderRadius: 1.5,
  },

  centerSection: {
    flex: 1,
    alignItems: 'center',
  },

  durationText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 2,
  },

  statusText: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)',
  },

  rightSection: {
    marginLeft: 12,
  },

  stopButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
