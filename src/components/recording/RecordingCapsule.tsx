import React, { useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  PanGestureHandler,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, spacing } from '../../styles';

interface RecordingCapsuleProps {
  visible: boolean;
  duration: number;
  isRecording: boolean;
  isPaused: boolean;
  onPress: () => void;
  onStop: () => void;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const RecordingCapsule: React.FC<RecordingCapsuleProps> = ({
  visible,
  duration,
  isRecording,
  isPaused,
  onPress,
  onStop,
}) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;

  // 格式化录音时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 处理拖拽手势
  const onGestureEvent = Animated.event(
    [{ nativeEvent: { translationX: translateX, translationY: translateY } }],
    { useNativeDriver: true }
  );

  const onHandlerStateChange = (event: any) => {
    if (event.nativeEvent.state === 5) { // GESTURE_STATE_END
      const { translationX: finalX, translationY: finalY } = event.nativeEvent;
      
      // 计算最终位置，确保胶囊不会超出屏幕边界
      const maxX = screenWidth - 200; // 胶囊宽度约200
      const maxY = screenHeight - 100; // 胶囊高度约50，加上安全边距
      
      const finalTranslateX = Math.max(-50, Math.min(maxX - 50, finalX));
      const finalTranslateY = Math.max(50, Math.min(maxY, finalY));

      // 动画到最终位置
      Animated.parallel([
        Animated.spring(translateX, {
          toValue: finalTranslateX,
          useNativeDriver: true,
        }),
        Animated.spring(translateY, {
          toValue: finalTranslateY,
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  if (!visible) return null;

  return (
    <PanGestureHandler
      onGestureEvent={onGestureEvent}
      onHandlerStateChange={onHandlerStateChange}
    >
      <Animated.View
        style={[
          styles.container,
          {
            transform: [
              { translateX },
              { translateY },
            ],
          },
        ]}
      >
        <TouchableOpacity style={styles.capsule} onPress={onPress} activeOpacity={0.8}>
          {/* 左侧录音状态指示器 */}
          <View style={styles.leftSection}>
            <View style={styles.statusIndicator}>
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 1 : 0.3 }]} />
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.7 : 0.2 }]} />
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.4 : 0.1 }]} />
            </View>
          </View>

          {/* 中间时长和状态 */}
          <View style={styles.centerSection}>
            <Text style={styles.durationText}>{formatDuration(duration)}</Text>
            <Text style={styles.statusText}>
              {isPaused ? '已暂停' : isRecording ? '录音中' : '准备录音'}
            </Text>
          </View>

          {/* 右侧停止按钮 */}
          <TouchableOpacity 
            style={styles.rightSection} 
            onPress={onStop}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <View style={styles.stopButton}>
              <Ionicons name="stop" size={16} color="#fff" />
            </View>
          </TouchableOpacity>
        </TouchableOpacity>
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 100, // 初始位置
    left: 20,
    zIndex: 1000,
  },

  capsule: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#007bff',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 180,
  },

  leftSection: {
    marginRight: 12,
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  recordingDot: {
    width: 3,
    height: 12,
    backgroundColor: '#fff',
    marginHorizontal: 1,
    borderRadius: 1.5,
  },

  centerSection: {
    flex: 1,
    alignItems: 'center',
  },

  durationText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 2,
  },

  statusText: {
    fontSize: 10,
    color: 'rgba(255, 255, 255, 0.8)',
  },

  rightSection: {
    marginLeft: 12,
  },

  stopButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
