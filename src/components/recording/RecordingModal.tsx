import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors, typography, spacing } from '../../styles';

interface RecordingModalProps {
  visible: boolean;
  onClose: () => void;
  onPause: () => void;
  onStop: () => void;
  isRecording: boolean;
  isPaused: boolean;
  duration: number;
  location?: string;
}

export const RecordingModal: React.FC<RecordingModalProps> = ({
  visible,
  onClose,
  onPause,
  onStop,
  isRecording,
  isPaused,
  duration,
  location,
}) => {
  const [transcriptionText, setTranscriptionText] = useState('');

  // 格式化录音时长
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 获取当前时间和日期
  const getCurrentDateTime = (): string => {
    const now = new Date();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hour = now.getHours().toString().padStart(2, '0');
    const minute = now.getMinutes().toString().padStart(2, '0');
    return `${month}-${day} ${hour}:${minute}·APP`;
  };

  // 模拟实时转录文本更新
  useEffect(() => {
    if (isRecording && !isPaused) {
      const mockTexts = [
        '大家好，很高兴大家今天抽空来参加这次会议，这次会议内容主要是上半年的工作总结，以及下半年的一个规划。',
        '首先请研发部张总来发言，听听他的想法。',
      ];
      
      let currentIndex = 0;
      let currentText = '';
      
      const interval = setInterval(() => {
        if (currentIndex < mockTexts.length) {
          const targetText = mockTexts[currentIndex];
          if (currentText.length < targetText.length) {
            currentText += targetText[currentText.length];
            setTranscriptionText(currentText);
          } else {
            currentIndex++;
            if (currentIndex < mockTexts.length) {
              currentText += '\n\n';
              setTranscriptionText(currentText);
            }
          }
        }
      }, 100);

      return () => clearInterval(interval);
    }
  }, [isRecording, isPaused]);

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      statusBarTranslucent
    >
      <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />
      <SafeAreaView style={styles.container}>
        {/* 头部 */}
        <View style={styles.header}>
          <Text style={styles.title}>新录音</Text>
          <TouchableOpacity style={styles.recordButton} onPress={onClose}>
            <Text style={styles.recordButtonText}>记一下</Text>
          </TouchableOpacity>
        </View>

        {/* 时间和位置信息 */}
        <View style={styles.infoContainer}>
          <View style={styles.infoRow}>
            <Ionicons name="calendar-outline" size={16} color={colors.text.secondary} />
            <Text style={styles.infoText}>{getCurrentDateTime()}</Text>
          </View>
          {location && (
            <View style={styles.infoRow}>
              <Ionicons name="location-outline" size={16} color={colors.text.secondary} />
              <Text style={styles.infoText}>{location}</Text>
            </View>
          )}
        </View>

        {/* 转录文本区域 */}
        <View style={styles.transcriptionContainer}>
          <Text style={styles.transcriptionText}>
            {transcriptionText || '开始录音后，实时转录内容将在这里显示...'}
          </Text>
          
          {/* 高亮显示最新的文本 */}
          {transcriptionText.includes('听听他的想法。') && (
            <View style={styles.highlightContainer}>
              <Text style={styles.highlightText}>听听他的想法。</Text>
            </View>
          )}
        </View>

        {/* 底部控制区域 */}
        <View style={styles.bottomContainer}>
          {/* 录音状态和时长 */}
          <View style={styles.statusContainer}>
            <View style={styles.statusIndicator}>
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 1 : 0.3 }]} />
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.7 : 0.2 }]} />
              <View style={[styles.recordingDot, { opacity: isRecording && !isPaused ? 0.4 : 0.1 }]} />
            </View>
            <Text style={styles.durationText}>{formatDuration(duration)}</Text>
            <Text style={styles.statusText}>
              {isPaused ? '已暂停' : isRecording ? '录音中' : '准备录音'}
            </Text>
          </View>

          {/* 控制按钮 */}
          <View style={styles.controlsContainer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Ionicons name="close" size={24} color="#fff" />
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.pauseButton, isPaused && styles.pauseButtonActive]} 
              onPress={onPause}
            >
              <Ionicons 
                name={isPaused ? "play" : "pause"} 
                size={32} 
                color="#fff" 
              />
            </TouchableOpacity>

            <TouchableOpacity style={styles.stopButton} onPress={onStop}>
              <Ionicons name="checkmark" size={24} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },

  title: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },

  recordButton: {
    backgroundColor: '#ffc107',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },

  recordButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text.primary,
  },

  infoContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: '#e9ecef',
  },

  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  infoText: {
    fontSize: 14,
    color: colors.text.secondary,
    marginLeft: 8,
  },

  transcriptionContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
  },

  transcriptionText: {
    fontSize: 16,
    lineHeight: 24,
    color: colors.text.primary,
    textAlign: 'left',
  },

  highlightContainer: {
    backgroundColor: '#fff3cd',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginTop: 8,
    alignSelf: 'flex-start',
  },

  highlightText: {
    fontSize: 16,
    color: '#856404',
    fontWeight: '500',
  },

  bottomContainer: {
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e9ecef',
  },

  statusContainer: {
    alignItems: 'center',
    paddingVertical: spacing.lg,
  },

  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },

  recordingDot: {
    width: 4,
    height: 16,
    backgroundColor: '#007bff',
    marginHorizontal: 2,
    borderRadius: 2,
  },

  durationText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#007bff',
    marginBottom: 4,
  },

  statusText: {
    fontSize: 14,
    color: colors.text.secondary,
  },

  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: spacing.md,
  },

  cancelButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#6c757d',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
  },

  pauseButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#007bff',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
  },

  pauseButtonActive: {
    backgroundColor: '#28a745',
  },

  stopButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#28a745',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: spacing.lg,
  },
});
