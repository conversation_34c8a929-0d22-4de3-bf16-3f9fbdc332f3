export interface Recording {
  id: string;
  title: string;
  filePath: string;
  duration: number;
  size: number;
  transcription?: string;
  summary?: string;
  aiAnalysis?: {
    keyPoints: string[];
    actionItems: string[];
    participants: string[];
    sentiment: 'positive' | 'neutral' | 'negative';
  };
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface RecordingState {
  isRecording: boolean;
  duration: number;
  isPaused: boolean;
  currentFile?: string;
}

export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  playbackRate: number;
}