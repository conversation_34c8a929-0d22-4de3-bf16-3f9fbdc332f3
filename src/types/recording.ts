export interface Recording {
  id: string;
  title: string;
  filePath: string;
  duration: number;
  size: number;
  transcription?: string;
  summary?: string;
  aiAnalysis?: {
    keyPoints: string[];
    actionItems: string[];
    participants: string[];
    sentiment: 'positive' | 'neutral' | 'negative';
  };
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  type?: 'import' | 'recording';
  // 转录状态
  transcriptionStatus: 'pending' | 'processing' | 'completed' | 'failed';
  transcriptionProgress?: number; // 转录进度 0-100
  location?: string; // 录音位置信息
}

export interface RecordingState {
  isRecording: boolean;
  duration: number;
  isPaused: boolean;
  currentFile?: string;
}

export interface PlaybackState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  playbackRate: number;
}
