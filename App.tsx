import React from 'react';
import 'react-native-url-polyfill/auto';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Toast from 'react-native-toast-message';
import { AppNavigator } from './src/navigation';
import { RecordingProvider } from './src/contexts/RecordingContext';
import { GlobalRecordingCapsule } from './src/components/recording/GlobalRecordingCapsule';

export default function App() {
  return (
    <SafeAreaProvider>
      <RecordingProvider>
        <AppNavigator />
        <GlobalRecordingCapsule />
        <StatusBar style="auto" />
        <Toast />
      </RecordingProvider>
    </SafeAreaProvider>
  );
}
