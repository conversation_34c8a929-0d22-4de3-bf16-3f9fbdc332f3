# 录音按钮样式更新

## 更新说明

根据提供的 CSS 代码，更新了录音按钮的样式，使其与设计规范保持一致。

## CSS 代码参考

```css
width: 203px;
height: 64px;
background: linear-gradient(177deg, #3053ED 0%, rgba(82,121,255,0.8) 50%, #3EDDB6 100%), #FFFFFF;
box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06);
border-radius: 20px 20px 20px 20px;
```

## iOS 代码参考（之前的参考）

```swift
let layerView = UIView()
layerView.frame = CGRect(x: 16, y: 106, width: 203, height: 64)

// 白色背景层
let bgLayer1 = CALayer()
bgLayer1.frame = layerView.bounds
bgLayer1.backgroundColor = UIColor(red: 1, green: 1, blue: 1, alpha: 1).cgColor

// 渐变层
let bgLayer2 = CAGradientLayer()
bgLayer2.colors = [
  UIColor(red: 0.19, green: 0.33, blue: 0.93, alpha: 1).cgColor,
  UIColor(red: 0.32, green: 0.47, blue: 1, alpha: 0.8).cgColor,
  UIColor(red: 0.24, green: 0.87, blue: 0.71, alpha: 1).cgColor
]
bgLayer2.locations = [0, 0.5, 1]
bgLayer2.startPoint = CGPoint(x: 0.49, y: 0.36)
bgLayer2.endPoint = CGPoint(x: 0.69, y: 0.69)

// 阴影
layerView.layer.shadowColor = UIColor(red: 0.39, green: 0.4, blue: 0.48, alpha: 0.06).cgColor
layerView.layer.shadowOffset = CGSize(width: 0, height: 1)
layerView.layer.shadowOpacity = 1
layerView.layer.shadowRadius = 4
```

## React Native 实现

### 1. 渐变色更新

```typescript
<LinearGradient
  colors={[
    '#3053ED',                    // #3053ED 0%
    'rgba(82,121,255,0.8)',      // rgba(82,121,255,0.8) 50%
    '#3EDDB6'                    // #3EDDB6 100%
  ]}
  locations={[0, 0.5, 1]}
  start={{ x: 0, y: 0 }}
  end={{ x: 1, y: 1 }}
  style={styles.gradientButton}
>
```

### 2. 按钮容器样式

```typescript
recordingActionButton: {
  width: 203,                   // 固定宽度 203px
  height: 64,                   // 固定高度 64px
  borderRadius: 20,
  overflow: 'hidden',
  paddingVertical: 0,
  paddingHorizontal: 0,
  // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
  shadowColor: 'rgba(100, 103, 122, 0.06)',
  shadowOffset: {
    width: 0,
    height: 1,
  },
  shadowOpacity: 1,
  shadowRadius: 4,
  elevation: 4, // Android 阴影
},
```

### 3. 渐变按钮样式

```typescript
gradientButton: {
  flex: 1,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  height: '100%',
  borderRadius: 20,
  // CSS: background: linear-gradient(...), #FFFFFF;
  // 白色作为后备背景色
  backgroundColor: '#FFFFFF',
},
```

## 颜色转换对照表

| iOS UIColor | React Native RGBA |
|-------------|-------------------|
| `UIColor(red: 0.19, green: 0.33, blue: 0.93, alpha: 1)` | `rgba(49, 84, 237, 1)` |
| `UIColor(red: 0.32, green: 0.47, blue: 1, alpha: 0.8)` | `rgba(82, 120, 255, 0.8)` |
| `UIColor(red: 0.24, green: 0.87, blue: 0.71, alpha: 1)` | `rgba(61, 221, 182, 1)` |
| `UIColor(red: 0.39, green: 0.4, blue: 0.48, alpha: 0.06)` | `rgba(99, 102, 122, 0.06)` |
| `UIColor(red: 1, green: 1, blue: 1, alpha: 1)` | `rgba(255, 255, 255, 1)` |

### 4. 导入音频按钮样式

```css
width: 132px;
height: 64px;
background: #FFFFFF;
box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06);
border-radius: 20px 20px 20px 20px;
```

```typescript
importAudioButton: {
  width: 132,                   // 固定宽度 132px
  height: 64,                   // 固定高度 64px
  backgroundColor: '#FFFFFF',   // background: #FFFFFF
  borderRadius: 20,             // border-radius: 20px
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  paddingVertical: 0,
  paddingHorizontal: 0,
  // box-shadow: 0px 1px 4px 0px rgba(100,103,122,0.06)
  shadowColor: 'rgba(100, 103, 122, 0.06)',
  shadowOffset: {
    width: 0,
    height: 1,
  },
  shadowOpacity: 1,
  shadowRadius: 4,
  elevation: 4, // Android 阴影
},
```

## 主要更新内容

### 录音按钮：
1. **渐变色**：更新为与 CSS 设计一致的三色渐变
2. **固定尺寸**：203x64 像素
3. **渐变位置**：设置渐变色的分布位置 `[0, 0.5, 1]`
4. **阴影效果**：添加与 CSS 一致的阴影效果
5. **背景层**：添加白色背景层作为渐变的基础

### 导入音频按钮：
1. **固定尺寸**：132x64 像素
2. **白色背景**：纯白色背景 `#FFFFFF`
3. **阴影效果**：与录音按钮相同的阴影规格
4. **移除边框**：使用阴影替代边框效果
5. **文本调整**：减小字体和间距以适应较小宽度

## 视觉效果

更新后的录音按钮将具有：
- 从蓝色到青绿色的渐变效果
- 轻微的阴影，增加立体感
- 圆角设计，更加现代化
- 与 iOS 设计规范完全一致的视觉效果

这些更新确保了录音按钮在不同平台上都能呈现一致的视觉效果。
